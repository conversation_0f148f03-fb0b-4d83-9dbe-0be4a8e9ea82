{"compilerOptions": {"typeRoots": ["./node_modules/@types", "./src/types"], "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "allowJs": true, "checkJs": false, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts", "types/mui-theme/theme.d.ts"], "exclude": ["node_modules", "dist", "build"]}