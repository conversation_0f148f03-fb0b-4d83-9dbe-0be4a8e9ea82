{"name": "trello-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "preview": "vite preview", "type-check": "tsc --noEmit", "check-all": "npm run type-check && npm run lint && npm run format:check", "fix-all": "npm run lint:fix && npm run format"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^7.1.0", "@mui/lab": "^7.0.0-beta.12", "@mui/material": "^7.1.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.30", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0"}}